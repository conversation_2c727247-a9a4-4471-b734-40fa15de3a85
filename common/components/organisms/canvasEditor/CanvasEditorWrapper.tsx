'use client'

import React, {
  useState, useEffect,
} from 'react';
import dynamic from 'next/dynamic';

// Dynamically import CanvasEditor to avoid SSR issues with Konva
const CanvasEditor = dynamic(
  () => import('./CanvasEditor').then((mod) => ({ default: mod.CanvasEditor })),
  {
    ssr: false,
    loading: () => (
      <div className="flex items-center justify-center h-full">
        <div className="text-white">Loading Canvas Editor...</div>
      </div>
    ),
  },
);

interface CanvasEditorWrapperProps {
  isOpen: boolean;
  onClose: () => void;
  agentId?: string;
  planId?: string;
  platform?: string;
}

export const CanvasEditorWrapper = (props: CanvasEditorWrapperProps) => {
  const [isClient, setIsClient] = useState(false);

  useEffect(() => {
    setIsClient(true);
  }, []);

  if (!isClient) {
    return (
      <div className="flex items-center justify-center h-full">
        <div className="text-white">Loading Canvas Editor...</div>
      </div>
    );
  }

  return <CanvasEditor {...props} />;
};
