'use client'

import React, {
  useState, useEffect,
} from 'react';
import Konva from 'konva';
import { Button } from '@/common/components/atoms';
import { motion } from "framer-motion";
import Link from 'next/link';
import { routes } from '@/common/routes';
import { ClientLogo } from '../header/ClientLogo';
import { secondaryFont } from '@/common/utils/localFont';

interface CanvasHeaderProps {
  onSaveDesign: () => void;
  canvas?: Konva.Stage | null;
}

export const CanvasHeader = ({
  onSaveDesign,
  canvas,
}: CanvasHeaderProps) => {
  const [selectedObject, setSelectedObject] = useState<Konva.Node | null>(null);
  const [canMoveUp, setCanMoveUp] = useState(false);
  const [canMoveDown, setCanMoveDown] = useState(false);

  useEffect(() => {
    if (!canvas) {
      return;
    }

    const updateSelection = () => {
      const transformer = canvas.findOne('Transformer') as Konva.Transformer;
      const activeObject = transformer?.nodes()[0] || null;
      setSelectedObject(activeObject);

      if (activeObject) {
        const layer = activeObject.getLayer();
        if (layer) {
          const objects = layer.children;
          const currentIndex = objects.findIndex((obj: any) => obj === activeObject);
          setCanMoveUp(currentIndex < objects.length - 1);
          setCanMoveDown(currentIndex > 0);
        }
      } else {
        setCanMoveUp(false);
        setCanMoveDown(false);
      }
    };

    // Listen for selection changes
    canvas.on('click', updateSelection);
    canvas.on('tap', updateSelection);

    return () => {
      canvas.off('click', updateSelection);
      canvas.off('tap', updateSelection);
    };
  }, [canvas]);

  const moveLayerUp = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    selectedObject.moveUp();
    canvas.batchDraw();

    // Update button states
    const layer = selectedObject.getLayer();
    if (layer) {
      const objects = layer.children;
      const currentIndex = objects.findIndex((obj: any) => obj === selectedObject);
      setCanMoveUp(currentIndex < objects.length - 1);
      setCanMoveDown(currentIndex > 0);
    }
  };

  const moveLayerDown = () => {
    if (!canvas || !selectedObject) {
      return;
    }

    selectedObject.moveDown();
    canvas.batchDraw();

    // Update button states
    const layer = selectedObject.getLayer();
    if (layer) {
      const objects = layer.children;
      const currentIndex = objects.findIndex((obj: any) => obj === selectedObject);
      setCanMoveUp(currentIndex < objects.length - 1);
      setCanMoveDown(currentIndex > 0);
    }
  };

  return (
    <div className="bg-eerie-black border-b border-neutral-700 px-4 py-2 md:py-3 flex items-center justify-between relative">
      <div className="flex items-center gap-1 md:gap-2">
        <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
          <ClientLogo width={24} height={24}/>
          <motion.span
            initial={{
              opacity: 0,
              translateX: 20,
            }}
            animate={{
              opacity: 1,
              translateX: 0,
            }}
            transition={{
              duration: 0.5,
              delay: 1,
            }}
            className={`text-white ${secondaryFont.className} animate-glowTransition`}
          >
            Media Pilot
          </motion.span>
        </Link>
        {selectedObject && (
          <>
            <Button
              onClick={moveLayerUp}
              disabled={!canMoveUp}
              variant="outline"
              size="sm"
              title="Bring to Front"
            >
              Bring to Front
            </Button>
            <Button
              onClick={moveLayerDown}
              disabled={!canMoveDown}
              variant="outline"
              size="sm"
              title="Send Back"
            >
              Send Back
            </Button>
          </>
        )}
      </div>
      <div className="flex items-center gap-1 md:gap-2">
        <Button
          variant="gradient"
          size="sm"
          onClick={onSaveDesign}
        >
          Save to Post
        </Button>
      </div>
    </div>
  );
};
