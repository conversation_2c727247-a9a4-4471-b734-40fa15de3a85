'use client'
import {
  useEffect,
  useState,
} from "react";
import {
  useSupabaseAuth, useToggle,
} from "@/common/hooks";
import { routes } from "@/common/routes";
import { motion } from "framer-motion";
import { secondaryFont } from "@/common/utils/localFont";
import { 
  usePathname, useSearchParams,
} from "next/navigation";
import Link from "next/link";
import {
  TooltipProvider,
} from '@/common/components/atoms';
import lang from "@/common/lang";
import { ClientLogo } from "./ClientLogo";
import {
  Button,
} from "../../atoms";
import { UserDropdown } from "@/common/components/molecules";
import { AuthModal } from "../modal/authModal";

const { header: headerCopy } = lang

export const Header = () => {
  const [isScrolled, setIsScrolled] = useState(false)
  const pathname = usePathname()
  // const [navbarOpened, toggleNavbar] = useToggle(false)
  const [navbarOpened] = useToggle(false)
  const [isAuthModalOpen, setIsAuthModalOpen] = useState(false)
  const searchParams = useSearchParams()
  const isImageModalOpen = searchParams.get('imageModal') === 'true';

  const isDashboardPage = pathname.includes("/dashboard")
  const {
    isAuthenticated, user, signOut,
  } = useSupabaseAuth()
  const isHomePage = false
  const handleScroll = () => {
    if (window.pageYOffset >= 50) {
      setIsScrolled(true)
    } else {
      setIsScrolled(false)
    }
  }
  useEffect(() => {
    window.addEventListener("scroll", handleScroll)
    return () => {
      window.removeEventListener("scroll", handleScroll)
    }
  }, [])

  // const openPage = () => {
  //   toggleNavbar(false)
  // }
  if (isImageModalOpen) {
    return <></>
  }
  return (
    <TooltipProvider>
      <nav className={`fixed top-0 left-0 right-0 z-[70] transition-all ${isImageModalOpen ? "px-0" : "px-2 md:px-4"} duration-200 ease-in-out ${isDashboardPage && !isScrolled ? "border-b border-violets-are-blue/15" : "border-transparent"} ${isScrolled ? "bg-violets-are-blue/5 backdrop-blur-sm" : ""} ${navbarOpened ? "bg-gradient-to-b from-eerie-black to-eerie-black/90 backdrop-blur-md h-[100dvh]" : ""}`}>
        <div className={`container mx-auto pb-3 relative ${isHomePage ? "pt-10" : "pt-3"} flex items-center justify-between`}>
          <div className="flex-1 flex gap-8">
            <Link href={routes.homePath} prefetch={true} replace className="flex gap-1 md:gap-2 items-center text-white font-semibold text-xl md:text-2xl">
              <ClientLogo />
              <motion.span
                initial={{
                  opacity: 0,
                  translateX: 20,
                }}
                animate={{
                  opacity: 1,
                  translateX: 0,
                }}
                transition={{
                  duration: 0.5,
                  delay: 1,
                }}
                className={`text-white ${secondaryFont.className} animate-glowTransition`}
              >
                Media Pilot
              </motion.span>
            </Link>
          </div>
          <>
            <motion.div initial={{
              opacity: 0,
            }}
            animate={{
              opacity: 1,
            }}
            transition={{
              duration: 0.5,
            }} className="flex gap-4 items-center">
              <div className="xl:hidden flex gap-2">
                {isAuthenticated ? (
                  <UserDropdown user={user} signOut={signOut} />
                ) : (
                  <>
                    <Button
                      type="button"
                      variant="outline"
                      size="lg"
                      onClick={() => setIsAuthModalOpen(true)}
                    >
                      {headerCopy.login}
                    </Button>
                  </>
                )}
              </div>
              {/* <button type="button" className="flex xl:hidden items-center justify-center" onClick={() => toggleNavbar(!navbarOpened)} >
                <NavIcon active={navbarOpened} />
              </button> */}
            </motion.div>
            <motion.div className="hidden xl:flex lg:mr-0 gap-2 sm:gap-2 items-center"
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              transition={{
                duration: 0.5,
              }}
            >
              {/* {authenticated && (
                <Button
                  variant="outline"
                  size="lg"
                  type="button"
                  onClick={() => router.push(routes.profilePath)}
                  className="max-lg:hover:bg-transparent"
                  onMouseEnter={() => toggleBookmarkAnim(true)}
                  onMouseLeave={() => toggleBookmarkAnim(false)}
                >
                  <span className={`h-6 w-6 ${secondaryFont.className} rounded-full hidden lg:flex bg-han-purple p-0.5`}>
                    <span className="w-full h-full rounded-full flex items-center justify-center shadow-sm text-white bg-gradient-to-tr from-violets-are-blue to-han-purple text-sm">{ownedIdeasCount}</span>
                  </span>
                  <span className="lg:inline hidden pl-1">{headerCopy.myDreams}</span>
                  <span className="lg:hidden"><BookmarkLottie enableAnim={bookmarkAnim} /></span>
                </Button>
              )}*/}
              {isAuthenticated ? (
                <UserDropdown user={user} signOut={signOut} />
              ) : (
                <>
                  <Button
                    type="button"
                    variant="outline"
                    size="lg"
                    onClick={() => setIsAuthModalOpen(true)}
                  >
                    {headerCopy.login}
                  </Button>
                </>
              )}
            </motion.div>
          </>
        </div>
        {/* {navbarOpened && (
          <div className={`absolute inset-0 top-16 flex flex-col justify-between overflow-y-auto bg-gradient-to-b from-eerie-black/95 to-eerie-black/90 backdrop-blur-md`}>
            <div className="w-full px-8 pt-6 pb-4">
              <motion.h3
                initial={{
                  opacity: 0,
                  y: -10,
                }}
                animate={{
                  opacity: 1,
                  y: 0,
                }}
                transition={{ duration: 0.3 }}
                className="text-white flex flex-col"
              >
                <span className="text-2xl font-medium">{headerCopy.bigger}</span>
                <span
                  className="text-5xl -translate-x-1 font-semibold bg-gradient-to-r from-violet-500 via-purple-500 to-indigo-500 text-transparent bg-clip-text animate-dreamyText leading-tight"
                >
                  {headerCopy.bigger}
                </span>
                <span className="text-2xl font-medium">{headerCopy.bigger}</span>
              </motion.h3>
            </div>

            <div className="flex-1 w-full px-8 py-6">
              <div className="space-y-5">
                {[
                  {
                    href: routes.viewProjectsPath,
                    label: "Dreams",
                    description: headerCopy.dreamsSubHeading,
                  },
                  {
                    href: routes.mediaPilotPath,
                    label: "Media Pilot",
                    description: headerCopy.pilotSubheading,
                  },
                  {
                    href: routes.dreamathonPath,
                    label: "Dreamathon",
                    description: headerCopy.dreamathonSubheading,
                  },
                ].map((item, index) => (
                  <motion.div
                    key={item.href}
                    initial={{
                      opacity: 0,
                      x: 20,
                    }}
                    animate={{
                      opacity: 1,
                      x: 0,
                    }}
                    transition={{
                      duration: 0.4,
                      delay: 0.3 + (index * 0.1),
                    }}
                  >
                    <Link
                      onClick={() => openPage()}
                      href={item.href} className="transition-all duration-200 ease-in-out text-gray-300 bg-gradient-to-tr from-violets-are-blue to-han-purple bg-clip-text hover:text-transparent font-medium flex items-center py-1 gap-3">
                      <div className="h-[52px] w-0.5 bg-gradient-to-tr from-han-purple to-tulip rounded-full"></div>
                      <div>
                        <div
                          className="!justify-start !px-0 !text-base !font-medium"
                        >
                          {item.label}
                        </div>
                        <p className="text-gray-400 text-xs mt-1">{item.description}</p>
                      </div>
                    </Link>
                  </motion.div>
                ))}
              </div>
            </div>

            <motion.div
              initial={{
                opacity: 0,
              }}
              animate={{
                opacity: 1,
              }}
              transition={{
                duration: 0.2,
                delay: 0.7,
              }}
              className="w-full px-8 py-6 space-y-4"
            >
              {authenticated && (
                <Button
                  variant="outline"
                  size="lg"
                  type="button"
                  width="w-full"
                  onClick={() => router.push(routes.profilePath)}
                  className="max-lg:hover:bg-transparent"
                  onMouseEnter={() => toggleBookmarkAnim(true)}
                  onMouseLeave={() => toggleBookmarkAnim(false)}
                >
                  <span className={`h-6 w-6 ${secondaryFont.className} rounded-full flex bg-han-purple p-0.5`}>
                    <span className="w-full h-full rounded-full flex items-center justify-center shadow-sm text-white bg-gradient-to-tr from-violets-are-blue to-han-purple text-sm">{ownedIdeasCount}</span>
                  </span>
                  <span className="pl-1">{headerCopy.myDreams}</span>
                </Button>
              )}
              <Button
                variant="gradient"
                size="lg"
                type="button"
                width="w-full"
                onClick={() => {
                  router.push(routes.createProjectPath)
                  toggleNavbar(false)
                }}
              >
                {headerCopy.generate}
              </Button>
            </motion.div>
          </div>
        )} */}
      </nav>
      <AuthModal
        isOpen={isAuthModalOpen}
        onClose={() => setIsAuthModalOpen(false)}
        initialTab="login"
      />
    </TooltipProvider>
  );
};
