'use client'

import { useWindowDimensions } from "@/common/hooks";
import { LogoIcon } from "@/common/components/icons";
import { motion } from "framer-motion";

export const ClientLogo = ({
  width = 32,
  height = 32,
} : {
  width?: number;
  height?: number;
}) => {
  const { 
    windowSize, isClient,
  } = useWindowDimensions();

  return (
    <motion.span
      initial={{
        opacity: 0,
        scale: 0,
      }}
      animate={{
        opacity: 1,
        scale: 1,
      }}
      transition={{
        duration: 0.5,
        delay: 1,
      }}
    >
      <LogoIcon
        width={isClient && windowSize === "mobile" ? 20 : width}
        height={isClient && (windowSize === "mobile" || windowSize === "tablet") ? 20 : height}
      />
    </motion.span>
  );
};
